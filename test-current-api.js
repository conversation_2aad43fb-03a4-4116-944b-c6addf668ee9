// Simple Node.js script to test current API endpoints
// Run with: node test-current-api.js

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('🚀 Testing Current Bus Tracking API...\n');

  // Test 1: Get all users
  try {
    console.log('1️⃣ Testing GET /api/admin/users');
    const response = await fetch(`${BASE_URL}/api/admin/users`);
    const users = await response.json();
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Users found: ${Array.isArray(users) ? users.length : 'Error'}`);
    if (Array.isArray(users) && users.length > 0) {
      console.log(`👤 Sample user roles: ${users.map(u => u.role).join(', ')}`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Get employees
  try {
    console.log('2️⃣ Testing GET /api/admin/users/employees');
    const response = await fetch(`${BASE_URL}/api/admin/users/employees`);
    const employees = await response.json();
    console.log(`✅ Status: ${response.status}`);
    console.log(`👷 Employees found: ${Array.isArray(employees) ? employees.length : 'Error'}`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Get buses
  try {
    console.log('3️⃣ Testing GET /api/admin/buses');
    const response = await fetch(`${BASE_URL}/api/admin/buses`);
    const buses = await response.json();
    console.log(`✅ Status: ${response.status}`);
    console.log(`🚌 Buses found: ${Array.isArray(buses) ? buses.length : 'Error'}`);
    if (Array.isArray(buses) && buses.length > 0) {
      console.log(`🚌 Sample bus: ${buses[0].bus_number || 'No bus number'}`);
      console.log(`🚌 Bus IDs for testing:`, buses.slice(0, 2).map(b => b.id));
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Get terminals
  try {
    console.log('4️⃣ Testing GET /api/admin/terminals');
    const response = await fetch(`${BASE_URL}/api/admin/terminals`);
    const terminals = await response.json();
    console.log(`✅ Status: ${response.status}`);
    console.log(`🏢 Terminals found: ${Array.isArray(terminals) ? terminals.length : 'Error'}`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 5: Get routes
  try {
    console.log('5️⃣ Testing GET /api/admin/routes');
    const response = await fetch(`${BASE_URL}/api/admin/routes`);
    const routes = await response.json();
    console.log(`✅ Status: ${response.status}`);
    console.log(`🛣️ Routes found: ${Array.isArray(routes) ? routes.length : 'Error'}`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 6: Test signup (create a test user)
  try {
    console.log('6️⃣ Testing POST /api/auth/signup (Test User)');
    const testUser = {
      email: `test_${Date.now()}@example.com`,
      password: 'testpass123',
      username: `testuser_${Date.now()}`,
      role: 'client',
      profile: {
        fullName: 'Test User',
        phone: '09171234567'
      }
    };

    const response = await fetch(`${BASE_URL}/api/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });

    const result = await response.json();
    console.log(`✅ Status: ${response.status}`);
    if (response.status === 201) {
      console.log(`👤 Test user created: ${testUser.email}`);
    } else {
      console.log(`⚠️ Response:`, result);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n' + '🎉 Testing Complete! 🎉');
  console.log('\n📋 Next Steps:');
  console.log('1. Run the database-updates.sql in Supabase');
  console.log('2. Implement the new employee endpoints');
  console.log('3. Test the new functionality');
}

// Check if fetch is available (Node 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ or install node-fetch');
  console.log('Alternative: Use the curl commands in api-testing.md');
  process.exit(1);
}

testAPI().catch(console.error);
